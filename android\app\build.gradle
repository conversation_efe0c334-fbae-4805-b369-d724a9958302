plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.google.aidy"
    compileSdkVersion 35
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    defaultConfig {
        applicationId = "com.google.aidy"
        minSdkVersion 26
        targetSdkVersion 35
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        multiDexEnabled = true
    }

    aaptOptions {
        noCompress 'task'
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.debug
        }
    }
}

repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    // MediaPipe and TensorFlow Lite dependencies
    implementation(name: 'tasks-genai-0.10.25', ext: 'aar')
    implementation(name: 'tasks-vision-0.10.21', ext: 'aar')
    implementation 'org.tensorflow:tensorflow-lite:2.13.0'
    implementation 'org.tensorflow:tensorflow-lite-gpu:2.13.0'
    
    // HTTP client for model download
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
    
    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1'
    
    // JSON parsing
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation "androidx.core:core-ktx:1.10.1"
    
    // Flutter dependencies
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.annotation:annotation:1.7.0'
}

flutter {
    source = "../.."
}
